name: Notify <PERSON>la<PERSON> and <PERSON>ar when PR is ready for review

on:
  workflow_call:
    secrets:
      AWS_S3_PR_ACCESS_KEY:
        required: true
      AWS_S3_PR_SECRET_KEY:
        required: true
      S3_PR_IMAGES_BUCKET_NAME:
        required: true
      LINEAR_API_TOKEN_CK:
        required: true
      LINEAR_API_TOKEN_JB:
        required: true
      LINEAR_API_TOKEN_DR:
        required: true
      LINEAR_API_TOKEN_CG:
        required: true
      PR_READY_FOR_REVIEW_SLACK_HOOK:
        required: true

jobs:
  notify:
    if: ${{ github.event.pull_request.draft == false }}
    runs-on: ubuntu-latest
    steps:
      # Step 1: Extract SAB-XXX from the PR title
      - name: Extract Linear Issue ID from PR Title
        id: extract_issue_id
        run: |
          if [[ "${{ github.event.pull_request.title }}" =~ (SAB-[0-9]+) ]]; then
            echo "issue_id=${BASH_REMATCH[1]}" >> $GITHUB_ENV
          else
            echo "No Linear issue ID found in PR title."
          fi

      # Step 2: Set LINEAR_API_TOKEN based on PR author or assignee
      - name: Set LINEAR_API_TOKEN based on PR author or assignee
        id: set_linear_token
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
        run: |
          # First try to match the PR author
          PR_AUTHOR="${{ github.event.pull_request.user.login }}"
          TOKEN_MATCHED=false

          case "$PR_AUTHOR" in
            calvinkarundu)
              echo "LINEAR_API_TOKEN=${{ secrets.LINEAR_API_TOKEN_CK }}" >> $GITHUB_ENV
              TOKEN_MATCHED=true
              echo "Matched LINEAR_API_TOKEN for author: $PR_AUTHOR"
              ;;
            jakobsababuu)
              echo "LINEAR_API_TOKEN=${{ secrets.LINEAR_API_TOKEN_JB }}" >> $GITHUB_ENV
              TOKEN_MATCHED=true
              echo "Matched LINEAR_API_TOKEN for author: $PR_AUTHOR"
              ;;
            crewsycrews)
              echo "LINEAR_API_TOKEN=${{ secrets.LINEAR_API_TOKEN_DR }}" >> $GITHUB_ENV
              TOKEN_MATCHED=true
              echo "Matched LINEAR_API_TOKEN for author: $PR_AUTHOR"
              ;;
            'codegen-sh[bot]')
              echo "LINEAR_API_TOKEN=${{ secrets.LINEAR_API_TOKEN_CG }}" >> $GITHUB_ENV
              TOKEN_MATCHED=true
              echo "Matched LINEAR_API_TOKEN for author: $PR_AUTHOR"
              ;;
          esac

          # If no match found for author, try to match assignees
          if [ "$TOKEN_MATCHED" = false ]; then
            echo "No matching LINEAR_API_TOKEN found for author: $PR_AUTHOR. Checking assignees..."

            # Fetch PR assignees using GitHub API
            ASSIGNEES=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
              "https://api.github.com/repos/$GITHUB_REPOSITORY/pulls/$PR_NUMBER" | jq -r '.assignees[].login // ""')

            echo "Found assignees: $ASSIGNEES"

            # Check each assignee for a match
            if [ -n "$ASSIGNEES" ]; then
              while IFS= read -r ASSIGNEE; do
                # Skip if empty
                [ -z "$ASSIGNEE" ] && continue

                case "$ASSIGNEE" in
                  calvinkarundu)
                    echo "LINEAR_API_TOKEN=${{ secrets.LINEAR_API_TOKEN_CK }}" >> $GITHUB_ENV
                    TOKEN_MATCHED=true
                    echo "Matched LINEAR_API_TOKEN for assignee: $ASSIGNEE"
                    break
                    ;;
                  jakobsababuu)
                    echo "LINEAR_API_TOKEN=${{ secrets.LINEAR_API_TOKEN_JB }}" >> $GITHUB_ENV
                    TOKEN_MATCHED=true
                    echo "Matched LINEAR_API_TOKEN for assignee: $ASSIGNEE"
                    break
                    ;;
                  crewsycrews)
                    echo "LINEAR_API_TOKEN=${{ secrets.LINEAR_API_TOKEN_DR }}" >> $GITHUB_ENV
                    TOKEN_MATCHED=true
                    echo "Matched LINEAR_API_TOKEN for assignee: $ASSIGNEE"
                    break
                    ;;
                esac
              done <<< "$ASSIGNEES"
            else
              echo "No assignees found for this PR."
            fi
          fi

          # If still no match found, exit with error
          if [ "$TOKEN_MATCHED" = false ]; then
            echo "No matching LINEAR_API_TOKEN found for author or assignees." && exit 1
          fi

      # Step 3: Configure AWS credentials
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_S3_PR_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_S3_PR_SECRET_KEY }}
          aws-region: us-east-1

      # Step 4: Process images in PR description
      - name: Process images in PR description
        id: process_images
        env:
          PR_BODY: ${{ github.event.pull_request.body }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
          S3_BUCKET: ${{ secrets.S3_PR_IMAGES_BUCKET_NAME }}
          AWS_REGION: us-east-1
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # Create a temporary directory for images
          mkdir -p /tmp/pr_images

          # Initialize modified body with original content
          MODIFIED_BODY="$PR_BODY"

          # Check if PR_BODY is empty or null
          if [ -z "$PR_BODY" ]; then
            echo "PR description is empty, nothing to process"
          else
            # Step 1: Extract image URLs from PR description (Markdown format)
            echo "Extracting image URLs from PR description..."
            IMAGE_URLS=$(echo "$PR_BODY" | grep -o -E '(!\[.*?\]\((https?://[^)]+)\)|<img.*?src="(https?://[^"]+)".*?>)' | sed -E 's/.*?(https?:\/\/[^)"]+).*/\1/g' || echo "")

            echo "Found original image URLs:"
            echo "$IMAGE_URLS"

            # Step 2: Get PR HTML body to extract actual image URLs
            echo "Fetching PR HTML body..."
            PR_HTML_BODY=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
              -H "Accept: application/vnd.github.v3.html+json" \
              "https://api.github.com/repos/$GITHUB_REPOSITORY/pulls/$PR_NUMBER" | jq -r '.body_html // ""')

            # Step 3: Extract image URLs from HTML body, excluding known badge domains
            echo "Extracting image URLs from HTML body..."
            ALL_HTML_URLS=$(echo "$PR_HTML_BODY" | grep -o -E 'src="(https?://[^"]+)"' | sed -E 's/src="(https?:\/\/[^"]+)"/\1/g' || echo "")

            # Filter out known badge and external service domains
            HTML_IMAGE_URLS=""
            while IFS= read -r url; do
              [ -z "$url" ] && continue
              # Skip badge domains and external services
              if [[ "$url" =~ (img\.shields\.io|camo\.githubusercontent\.com|badge\.fury\.io|travis-ci\.org|circleci\.com|codecov\.io|coveralls\.io) ]]; then
                echo "Skipping badge/external URL: $url"
                continue
              fi
              # Add to filtered list
              if [ -z "$HTML_IMAGE_URLS" ]; then
                HTML_IMAGE_URLS="$url"
              else
                HTML_IMAGE_URLS="$HTML_IMAGE_URLS"$'\n'"$url"
              fi
            done <<< "$ALL_HTML_URLS"

            echo "Found HTML image URLs (after filtering):"
            echo "$HTML_IMAGE_URLS"

            # Step 4: Process images without relying on count matching
            ORIG_COUNT=$(echo "$IMAGE_URLS" | grep -v '^$' | wc -l)
            HTML_COUNT=$(echo "$HTML_IMAGE_URLS" | grep -v '^$' | wc -l)
            echo "Original URLs: $ORIG_COUNT, HTML URLs: $HTML_COUNT (counts don't need to match)"

            # Process each HTML image URL (these are the actual URLs we need to download)
            if [ -n "$HTML_IMAGE_URLS" ]; then
              echo "Found images in PR description, processing..."

              while IFS= read -r IMAGE_URL; do
                # Skip if empty
                [ -z "$IMAGE_URL" ] && continue

                echo "Processing image: $IMAGE_URL"

                # Generate unique filename
                TIMESTAMP=$(date +%s)
                URL_HASH=$(echo "$IMAGE_URL" | md5sum | cut -d' ' -f1 | cut -c1-10)

                # Create a temporary file for the image download
                TEMP_FILE=$(mktemp)

                # Download the image
                curl -s -L -o "$TEMP_FILE" "$IMAGE_URL"

                # Check if download succeeded
                if [ $? -eq 0 ] && [ -s "$TEMP_FILE" ]; then
                  echo "Downloaded image to temporary file"

                  # Detect file type using file command
                  FILE_TYPE=$(file -b --mime-type "$TEMP_FILE")
                  echo "Detected file type: $FILE_TYPE"

                  # Skip if not an image
                  if [[ "$FILE_TYPE" != image/* ]]; then
                    echo "Downloaded file is not an image (type: $FILE_TYPE). Skipping."
                    rm -f "$TEMP_FILE"
                    continue
                  fi

                  # Determine extension based on mime type
                  case "$FILE_TYPE" in
                    image/jpeg) EXT="jpg" ;;
                    image/png) EXT="png" ;;
                    image/gif) EXT="gif" ;;
                    image/webp) EXT="webp" ;;
                    image/svg+xml) EXT="svg" ;;
                    *) EXT="jpg" ;; # Default to jpg for other image types
                  esac

                  FILENAME="pr_${PR_NUMBER}_${TIMESTAMP}_${URL_HASH}.${EXT}"
                  LOCAL_PATH="/tmp/pr_images/$FILENAME"

                  # Move the temp file to the proper location with correct extension
                  mv "$TEMP_FILE" "$LOCAL_PATH"

                  # Upload to S3 with content-type
                  echo "Uploading to S3..."
                  if aws s3 cp "$LOCAL_PATH" "s3://$S3_BUCKET/$FILENAME" --content-type "$FILE_TYPE"; then
                    echo "Upload successful"
                    # Get S3 URL with correct region
                    S3_URL="https://$S3_BUCKET.s3.us-east-1.amazonaws.com/$FILENAME"
                    echo "S3 URL: $S3_URL"

                    # Find corresponding original URL by trying to match the HTML URL with any original URL
                    # This removes the dependency on equal counts
                    ORIG_URL=""
                    while IFS= read -r orig_url; do
                      [ -z "$orig_url" ] && continue
                      # Check if this HTML URL could correspond to this original URL
                      # For GitHub user-attachments, the HTML URL should be the same as the original
                      if [[ "$IMAGE_URL" == "$orig_url" ]] || [[ "$orig_url" =~ github\.com.*user-attachments.*assets ]]; then
                        ORIG_URL="$orig_url"
                        break
                      fi
                    done <<< "$IMAGE_URLS"

                    if [ ! -z "$ORIG_URL" ]; then
                      # Escape special characters in URLs for sed
                      ORIG_URL_ESCAPED=$(printf '%s\n' "$ORIG_URL" | sed -e 's/[\/&]/\\&/g')
                      S3_URL_ESCAPED=$(printf '%s\n' "$S3_URL" | sed -e 's/[\/&]/\\&/g')

                      # Replace URL in body using escaped values
                      MODIFIED_BODY=$(echo "$MODIFIED_BODY" | sed "s|$ORIG_URL_ESCAPED|$S3_URL_ESCAPED|g")
                      echo "Replaced URL in PR description: $ORIG_URL -> $S3_URL"
                    else
                      echo "Could not find matching original URL for: $IMAGE_URL"
                      # Try to replace the HTML URL directly if it appears in the markdown
                      IMAGE_URL_ESCAPED=$(printf '%s\n' "$IMAGE_URL" | sed -e 's/[\/&]/\\&/g')
                      S3_URL_ESCAPED=$(printf '%s\n' "$S3_URL" | sed -e 's/[\/&]/\\&/g')
                      MODIFIED_BODY=$(echo "$MODIFIED_BODY" | sed "s|$IMAGE_URL_ESCAPED|$S3_URL_ESCAPED|g")
                      echo "Attempted direct replacement: $IMAGE_URL -> $S3_URL"
                    fi
                  else
                    echo "Failed to upload to S3. Error code: $?"
                    echo "Keeping original URL"
                  fi
                else
                  echo "Failed to download image, keeping original URL"
                  rm -f "$TEMP_FILE"
                fi
              done <<< "$HTML_IMAGE_URLS"
            else
              echo "No images found in PR description"
            fi
          fi

          # Save modified body to environment
          echo "MODIFIED_BODY<<EOF" >> $GITHUB_ENV
          echo "$MODIFIED_BODY" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      # Step 5: Post PR description as a comment on the Linear issue
      - name: Post PR description to Linear
        if: env.issue_id != ''  # Proceed only if an issue ID was found
        run: |
          # Set variables
          ISSUE_ID=$issue_id

          # Convert HTML image tags to Markdown format, preserving existing Markdown images
          # This only converts HTML image tags and leaves Markdown image syntax unchanged
          COMMENT_TEXT=$(echo "$MODIFIED_BODY" | sed -E 's/<img[^>]*src="([^"]*)"[^>]*>/![](\1)/g')

          # Escape COMMENT_TEXT for JSON compatibility using jq
          ESCAPED_COMMENT_TEXT=$(jq -Rsa . <<< "$COMMENT_TEXT")

          # Post the comment using the Linear API
          curl -X POST https://api.linear.app/graphql \
            -H "Authorization: $LINEAR_API_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
              "query": "mutation ($input: CommentCreateInput!) { commentCreate(input: $input) { success comment { id } } }",
              "variables": {
                "input": {
                  "body": '"${ESCAPED_COMMENT_TEXT}"',
                  "issueId": "'"${ISSUE_ID}"'"
                }
              }
            }'

      # Step 6: Send notification to Slack
      - name: Send notification to Slack
        run: |
          REPO_NAME=$(echo "${{ github.repository }}" | cut -d'/' -f2)

          # Process PR description for better Slack formatting
          SLACK_DESCRIPTION="$MODIFIED_BODY"

          # Convert markdown headers to uppercase for better visibility in Slack
          # First, clean up any trailing whitespace before processing headers
          SLACK_DESCRIPTION=$(echo "$SLACK_DESCRIPTION" | sed -E 's/[[:space:]]+$//' | sed -E 's/^### (.*)/▪ \1/g' | sed -E 's/^## (.*)/\U\1\E/g' | sed -E 's/^# (.*)/\U\1\E/g')

          # Convert markdown images to just URLs for Slack (![alt](url) -> url)
          SLACK_DESCRIPTION=$(echo "$SLACK_DESCRIPTION" | sed -E 's/!\[[^\]]*\]\(([^)]+)\)/\1/g')

          # Convert markdown links back to Slack format since we'll enable mrkdwn parsing ([text](url) -> <url|text>)
          SLACK_DESCRIPTION=$(echo "$SLACK_DESCRIPTION" | sed -E 's/\[([^\]]+)\]\(([^)]+)\)/<\2|\1>/g')

          # Convert markdown bold to Slack format (**text** -> *text*)
          # Also handle cases where there might be trailing whitespace before closing asterisks
          SLACK_DESCRIPTION=$(echo "$SLACK_DESCRIPTION" | sed -E 's/\*\*([^*]+[^*[:space:]])[[:space:]]*\*\*/\*\1\*/g' | sed -E 's/\*\*([^*]+)\*\*/\*\1\*/g')

          # Clean up extra whitespace and ensure proper line breaks
          SLACK_DESCRIPTION=$(echo "$SLACK_DESCRIPTION" | sed -E 's/[[:space:]]+$//g' | sed -E '/^$/d')

          # Limit description length to prevent Slack message size limits (max ~40000 chars, but let's be conservative)
          if [ ${#SLACK_DESCRIPTION} -gt 3000 ]; then
            SLACK_DESCRIPTION="${SLACK_DESCRIPTION:0:2950}... [truncated]"
          fi

          # Escape PR description for JSON compatibility using jq
          ESCAPED_PR_DESCRIPTION=$(jq -Rsa . <<< "$SLACK_DESCRIPTION")

          # Create Slack webhook payload (webhooks don't support top-level mrkdwn/parse fields)
          json=$(jq -n \
            --arg base "${{ github.event.pull_request.base.ref }}" \
            --arg repo "$REPO_NAME" \
            --arg user "${{ github.event.pull_request.user.login }}" \
            --arg title "${{ github.event.pull_request.title }}" \
            --arg url "${{ github.event.pull_request.html_url }}" \
            --argjson description "$ESCAPED_PR_DESCRIPTION" \
            '{
              base: $base,
              repo: $repo,
              user: $user,
              pull_request_title: $title,
              pull_request_url: $url,
              pull_request_description: $description
            }')
          curl -X POST -H 'Content-type: application/json' --data "$json" ${{ secrets.PR_READY_FOR_REVIEW_SLACK_HOOK }}

name: Create GitHub Release

on:
  workflow_call:
    inputs:
      release_label:
        description: 'Label name that triggers release creation'
        required: false
        type: string
        default: 'Release'

jobs:
  create-release:
    if: github.event.pull_request.merged == true && contains(github.event.pull_request.labels.*.name, inputs.release_label)
    runs-on: ubuntu-latest
    
    permissions:
      contents: write
      pull-requests: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Get latest release
        id: latest_release
        run: |
          # Get the latest release tag, or use a fallback if no releases exist
          LATEST_TAG=$(gh release list --limit 1 --json tagName --jq '.[0].tagName' 2>/dev/null || echo "")
          if [ -z "$LATEST_TAG" ]; then
            # If no releases exist, use the first commit as the starting point
            LATEST_TAG=$(git rev-list --max-parents=0 HEAD)
            echo "No previous releases found, using first commit: $LATEST_TAG"
          fi
          echo "latest_tag=$LATEST_TAG" >> $GITHUB_OUTPUT
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Generate release tag
        id: generate_tag
        run: |
          # Generate a timestamp-based tag for the release
          TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
          TAG="release-$TIMESTAMP"
          echo "tag=$TAG" >> $GITHUB_OUTPUT
          echo "Generated release tag: $TAG"
      
      - name: Create GitHub Release
        id: create_release
        run: |
          # Create the release with auto-generated changelog
          gh release create "${{ steps.generate_tag.outputs.tag }}" \
            --title "Release ${{ steps.generate_tag.outputs.tag }}" \
            --generate-notes \
            --target "${{ github.event.pull_request.base.ref }}"
          
          echo "Release created successfully!"
          echo "release_url=$(gh release view ${{ steps.generate_tag.outputs.tag }} --json url --jq '.url')" >> $GITHUB_OUTPUT
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Comment on PR
        if: steps.create_release.outcome == 'success'
        run: |
          gh pr comment ${{ github.event.pull_request.number }} \
            --body "🚀 **GitHub Release Created!**
          
          A new GitHub release has been automatically created for this PR:
          **[${{ steps.generate_tag.outputs.tag }}](${{ steps.create_release.outputs.release_url }})**
          
          The release includes all changes since the last GitHub release and uses auto-generated release notes."
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

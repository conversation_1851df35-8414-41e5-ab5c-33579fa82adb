<?php

declare(strict_types=1);

namespace App\Http\Resources\Admin;

use App\Http\Resources\User\OwnUserResource;
use App\Http\Resources\User\GuestUserResource;
use App\Services\User\PersonVerify\OriginalPhoto;
use App\Services\User\PersonVerify\VerifyPhoto;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class VerificationRequestedResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        if ($this->profile) {
            $idPhotoPath = OriginalPhoto::FOLDER_NAME . '/' . $this->profile->id . '/' . OriginalPhoto::FILE_NAME;
            $photos = Storage::allFiles(OriginalPhoto::FOLDER_NAME . '/' . $this->profile->id);
            foreach ($photos as $photo) {
                if ($photo !== $idPhotoPath) {
                    $selfiePhotoPath = $photo;
                    $photoName = substr(strrchr($selfiePhotoPath, "/"), 1);
                    $rule = stristr($photoName, VerifyPhoto::FILE_EXTENSION, true);
                }
            }
        }

        return [
            'id_photo' => isset($selfiePhotoPath) ? Storage::url($idPhotoPath) : null,
            'selfie_photo' => isset($selfiePhotoPath) ? Storage::url($selfiePhotoPath) : null,
            'rule' => isset($selfiePhotoPath) ? $rule : null,
            'user' => new GuestUserResource($this),
            'profile' => $this->profile ? new OwnUserResource($this->profile) : null
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Services\Admin;

use App\Enums\Profile\Verify\VerificationStatus;
use App\Models\User;
use App\Services\Wallet\WalletService;

class UserInfo
{
    public function __construct(private readonly User $user)
    {
    }

    public function getInfo(): array
    {
        $isVerified = $this->user->isVerified();
        return [
            'phone_number' => $this->user->phone,
            'profile_completed' => $this->user->profile()->exists(),
            'phone_verified' => $this->user->hasVerifiedPhone(),
            'person_verified' => $isVerified,
            'credits_balance' => $this->user->profile?->wallet->credits,
            'credits_spent' => $this->getCreditsSpent(),
            'number_active_conversations' => $this->activeDialogs()
        ];
    }

    private function getCreditsSpent(): ?int
    {
        if (!$this->user->profile) {
            return null;
        }

        return (new WalletService($this->user->profile->wallet))->countCreditsSpent();
    }

    private function activeDialogs(): ?int
    {
        return $this->user->profile?->allDialogs()
            ->where('block', 0)
            ->count();
    }
}
